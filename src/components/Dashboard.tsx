import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { PartnerResources } from "./PartnerResources";

interface DashboardProps {
  setActiveTab?: (tab: string) => void;
}

export function Dashboard({ setActiveTab }: DashboardProps) {
  const user = useQuery(api.users.myProfile);
  const leads = useQuery(api.leads.listMine, {});
  const deals = useQuery(api.deals.listForPartner, {});
  const earnings = useQuery(api.earnings.overview, {});

  if (!user || leads === undefined || deals === undefined || earnings === undefined) {
    return (
      <div className="flex justify-center items-center py-grid-8">
        <div className="flex flex-col items-center gap-grid-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-accent border-t-transparent"></div>
          <p className="text-size-4 text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Debug logging to check earnings calculation
  console.log('🔍 Dashboard Debug Info:');
  console.log('📊 Deals:', deals);
  console.log('💰 Earnings:', earnings);
  console.log('👤 User tier:', user.tier);
  console.log('🔧 Dashboard Component Loaded Successfully');

  // Check each deal's commission values
  deals.forEach(deal => {
    const totalCommission = (deal.commissionDueTokenUsd || 0) + (deal.commissionDueFiatUsd || 0) + (deal.commissionPendingUsd || 0);
    console.log(`📋 Deal "${deal.projectName}":`, {
      status: deal.status,
      dealValue: deal.dealValueUsd,
      commissionDueToken: deal.commissionDueTokenUsd,
      commissionDueFiat: deal.commissionDueFiatUsd,
      commissionPending: deal.commissionPendingUsd,
      totalCommission,
      contributes: (deal.status === "closed" || deal.status === "paid") ? 'YES to pending' : 'NO'
    });
  });

  const getTierInfo = (tier: string | undefined) => {
    const baseStyle = "px-grid-3 py-grid-1 rounded-lg text-size-4 font-semibold";
    switch (tier) {
      case "elite":
        return {
          name: "Elite Partner",
          commission: "12%",
          icon: "💎",
          badge: `${baseStyle} bg-accent/20 text-accent-foreground`
        };
      case "diamond":
        return {
          name: "Diamond Partner",
          commission: "15%",
          icon: "👑",
          badge: `${baseStyle} bg-accent text-accent-foreground`
        };
      case "trusted":
        return {
          name: "Trusted Partner",
          commission: "10%",
          icon: "🤝",
          badge: `${baseStyle} bg-muted/20 text-card-foreground`
        };
      default:
        return {
          name: "Partner",
          commission: "8%",
          icon: "🤝",
          badge: `${baseStyle} bg-muted/20 text-card-foreground`
        };
    }
  };

  const tierInfo = getTierInfo(user.tier);

  const stats = [
    {
      label: "Total Leads",
      value: leads.length,
      icon: "👥",
      color: "text-card-foreground",
    },
    {
      label: "Active Deals",
      value: deals.filter(d => d.status === "in_progress").length,
      icon: "💼",
      color: "text-card-foreground",
    },
    {
      label: "Closed Deals",
      value: deals.filter(d => d.status === "closed" || d.status === "paid").length,
      icon: "✅",
      color: "text-card-foreground",
    },
    {
      label: "Pending Earnings",
      value: `$${earnings.pending.toLocaleString()}`,
      icon: "💰",
      color: "text-accent",
    },
  ];

  return (
    <div className="space-y-grid-8">
      {/* Header Section */}
      <div className="space-y-grid-2">
        <h1 className="text-size-1">Dashboard Overview</h1>
        <p className="text-size-3 text-muted-foreground">Welcome back! Here's your current status and performance.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-grid-4 lg:gap-grid-6">
        {stats.map((stat, index) => (
          <div key={index} className="card group">
            <div className="card-content space-y-grid-4">
              <div className="flex items-center justify-between">
                <div className={`inline-flex items-center px-grid-3 py-grid-1 rounded-lg text-size-4 font-normal bg-muted/20 ${stat.color}`}>
                  <span className="mr-grid-2">{stat.icon}</span>
                  {stat.label}
                </div>
              </div>
              <div className="space-y-grid-1">
                <p className="text-size-1 group-hover:text-accent transition-colors">
                  {stat.value}
                </p>
                <p className="text-size-4 text-muted-foreground">
                  Current total
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid lg:grid-cols-2 gap-grid-6">
        {/* Recent Leads */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h2 className="text-size-2">Recent Leads</h2>
              <span className="text-size-4 text-muted-foreground">{leads.length} total</span>
            </div>
          </div>
          <div className="card-content">
            {leads.length === 0 ? (
              <div className="text-center py-grid-8 space-y-grid-4">
                <div className="w-12 h-12 bg-muted/20 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-xl">👥</span>
                </div>
                <div className="space-y-grid-2">
                  <p className="text-size-3 text-muted-foreground">No leads submitted yet</p>
                  <p className="text-size-4 text-muted-foreground">Start by submitting your first lead to begin earning commissions</p>
                </div>
              </div>
            ) : (
              <div className="space-y-grid-3">
                {leads.slice(0, 5).map((lead) => (
                  <div key={lead._id} className="flex justify-between items-center py-grid-3 border-b border-border last:border-b-0">
                    <div className="space-y-grid-1">
                      <p className="text-size-3 font-normal">{lead.company}</p>
                      <p className="text-size-4 text-muted-foreground">{lead.status}</p>
                    </div>
                    <span className={`px-grid-2 py-grid-1 rounded-lg text-size-4 font-normal ${
                      lead.approved === true ? "bg-accent/20 text-accent" :
                      lead.approved === false ? "bg-destructive/20 text-destructive" :
                      "bg-muted/20 text-card-foreground"
                    }`}>
                      {lead.approved === true ? "✅ Approved" :
                       lead.approved === false ? "❌ Rejected" : "⏳ Pending"}
                    </span>
                  </div>
                ))}
                {leads.length > 5 && (
                  <div className="pt-grid-2">
                    <p className="text-size-4 text-muted-foreground text-center">
                      +{leads.length - 5} more leads
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Recent Deals */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h2 className="text-size-2">Recent Deals</h2>
              <span className="text-size-4 text-muted-foreground">{deals.length} total</span>
            </div>
          </div>
          <div className="card-content">
            {deals.length === 0 ? (
              <div className="text-center py-grid-8 space-y-grid-4">
                <div className="w-12 h-12 bg-muted/20 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-xl">💼</span>
                </div>
                <div className="space-y-grid-2">
                  <p className="text-size-3 text-muted-foreground">No deals yet</p>
                  <p className="text-size-4 text-muted-foreground">Deals will appear here once your leads are approved and converted</p>
                </div>
              </div>
            ) : (
              <div className="space-y-grid-3">
                {deals.slice(0, 5).map((deal) => (
                  <div key={deal._id} className="flex justify-between items-center py-grid-3 border-b border-border last:border-b-0">
                    <div className="space-y-grid-1">
                      <p className="text-size-3 font-normal">{deal.projectName}</p>
                      <p className="text-size-4 text-muted-foreground">
                        {deal.dealValueUsd ? `$${deal.dealValueUsd.toLocaleString()}` : "Value TBD"}
                      </p>
                    </div>
                    <span className={`px-grid-2 py-grid-1 rounded-lg text-size-4 font-normal ${
                      deal.status === "closed" || deal.status === "paid" ? "bg-accent/20 text-accent" :
                      deal.status === "in_progress" ? "bg-muted/20 text-card-foreground" :
                      "bg-muted/20 text-muted-foreground"
                    }`}>
                      {deal.status === "closed" ? "✅ Closed" :
                       deal.status === "paid" ? "💰 Paid" :
                       deal.status === "in_progress" ? "🔄 In Progress" :
                       deal.status}
                    </span>
                  </div>
                ))}
                {deals.length > 5 && (
                  <div className="pt-grid-2">
                    <p className="text-size-4 text-muted-foreground text-center">
                      +{deals.length - 5} more deals
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Partner Resources */}
      <PartnerResources />
    </div>
  );
}
