import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { toast } from "sonner";

interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  discount: number;
  total: number;
}

interface InvoiceData {
  invoiceNumber?: string;
  dueDate: string;
  notes?: string;
  lineItems: InvoiceLineItem[];
  vatRate: number;
  paymentTerms: string;
}

interface InvoiceEditorProps {
  deal: {
    _id: Id<"deals">;
    projectName: string;
    status: "in_progress" | "closed" | "lost" | "paid";
    commissionDueFiatUsd?: number;
    commissionDueTokenUsd?: number;
    invoiceNumber?: string;
    invoiceStatus?: "draft" | "sent" | "paid" | "overdue" | "cancelled";
    invoiceGeneratedAt?: number;
    invoiceDueDate?: number;
    invoiceNotes?: string;
    dealValueUsd?: number;
    commissionPct?: number;
  };
  onSave?: (invoiceData: InvoiceData) => void;
  onCancel?: () => void;
}

export function InvoiceEditor({ deal, onSave, onCancel }: InvoiceEditorProps) {
  const generateInvoice = useMutation(api.deals.generateInvoice);
  const partnerData = useQuery(api.deals.getInvoiceData, { dealId: deal._id });
  
  const totalCommission = (deal.commissionDueFiatUsd || 0) + (deal.commissionDueTokenUsd || 0);
  
  // Initialize default invoice data, pre-filling if invoice exists
  const [invoiceData, setInvoiceData] = useState<InvoiceData>({
    dueDate: deal.invoiceDueDate 
      ? new Date(deal.invoiceDueDate).toISOString().split('T')[0]
      : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    notes: deal.invoiceNotes || "",
    vatRate: 0,
    paymentTerms: "Net 30",
    lineItems: [{
      id: "1",
      description: `Business Development Services - ${deal.projectName}`,
      quantity: 1,
      unitPrice: totalCommission,
      discount: 0,
      total: totalCommission
    }]
  });

  const [isGenerating, setIsGenerating] = useState(false);

  // Update line item totals when values change
  const updateLineItem = (id: string, field: keyof InvoiceLineItem, value: string | number) => {
    setInvoiceData(prev => ({
      ...prev,
      lineItems: prev.lineItems.map(item => {
        if (item.id !== id) return item;
        
        const updated = { ...item, [field]: value };
        
        // Recalculate total when quantity, unitPrice, or discount changes
        if (field === 'quantity' || field === 'unitPrice' || field === 'discount') {
          const subtotal = updated.quantity * updated.unitPrice;
          const discountAmount = subtotal * (updated.discount / 100);
          updated.total = subtotal - discountAmount;
        }
        
        return updated;
      })
    }));
  };

  const addLineItem = () => {
    const newId = (invoiceData.lineItems.length + 1).toString();
    setInvoiceData(prev => ({
      ...prev,
      lineItems: [...prev.lineItems, {
        id: newId,
        description: "",
        quantity: 1,
        unitPrice: 0,
        discount: 0,
        total: 0
      }]
    }));
  };

  const removeLineItem = (id: string) => {
    if (invoiceData.lineItems.length <= 1) return; // Keep at least one item
    setInvoiceData(prev => ({
      ...prev,
      lineItems: prev.lineItems.filter(item => item.id !== id)
    }));
  };

  // Calculate totals
  const subtotal = invoiceData.lineItems.reduce((sum, item) => sum + item.total, 0);
  const vatAmount = subtotal * (invoiceData.vatRate / 100);
  const total = subtotal + vatAmount;

  const handleSave = async () => {
    if (invoiceData.lineItems.some(item => !item.description.trim())) {
      toast.error("Please fill in all line item descriptions");
      return;
    }

    if (total <= 0) {
      toast.error("Invoice total must be greater than 0");
      return;
    }

    setIsGenerating(true);
    try {
      const dueDateTimestamp = new Date(invoiceData.dueDate).getTime();
      
      await generateInvoice({
        dealId: deal._id,
        invoiceNotes: invoiceData.notes?.trim() || undefined,
        dueDate: dueDateTimestamp,
      });
      
      toast.success("Invoice generated successfully!");
      onSave?.(invoiceData);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to generate invoice");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="glass-card max-w-6xl w-full max-h-[90vh] overflow-y-auto">
      <div className="card-content">
        {/* Header */}
        <div className="flex items-center justify-between mb-grid-6">
          <div className="flex items-center gap-grid-3">
            <div className="w-10 h-10 bg-gradient-accent rounded-lg flex items-center justify-center">
              <span className="text-accent-foreground text-size-2">📝</span>
            </div>
            <div>
              <h2 className="text-size-2">Invoice Editor</h2>
              <p className="text-size-4 text-muted-foreground">
                {deal.projectName} • ${totalCommission.toLocaleString()} commission
              </p>
            </div>
          </div>
          <button
            onClick={onCancel}
            className="text-muted-foreground hover:text-foreground"
          >
            ✕
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-grid-6">
          {/* Left Column - Invoice Form */}
          <div className="space-y-grid-6">
            {/* Invoice Details */}
            <div className="glass-card">
              <div className="card-content">
                <h3 className="text-size-3 font-semibold mb-grid-4">Invoice Details</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-4">
                  <div className="form-floating">
                    <input
                      type="date"
                      value={invoiceData.dueDate}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, dueDate: e.target.value }))}
                      className="hover-glow"
                      min={new Date().toISOString().split('T')[0]}
                    />
                    <label>Due Date</label>
                  </div>

                  <div className="form-floating">
                    <select
                      value={invoiceData.paymentTerms}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, paymentTerms: e.target.value }))}
                      className="hover-glow"
                    >
                      <option value="Net 15">Net 15 days</option>
                      <option value="Net 30">Net 30 days</option>
                      <option value="Net 60">Net 60 days</option>
                      <option value="Due on receipt">Due on receipt</option>
                    </select>
                    <label>Payment Terms</label>
                  </div>
                </div>

                <div className="form-floating">
                  <input
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={invoiceData.vatRate}
                    onChange={(e) => setInvoiceData(prev => ({ ...prev, vatRate: parseFloat(e.target.value) || 0 }))}
                    className="hover-glow"
                  />
                  <label>VAT Rate (%)</label>
                </div>

                <div className="form-floating">
                  <textarea
                    value={invoiceData.notes || ""}
                    onChange={(e) => setInvoiceData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder=" "
                    rows={3}
                    className="hover-glow resize-none"
                  />
                  <label>Invoice Notes (Optional)</label>
                </div>
              </div>
            </div>

            {/* Line Items */}
            <div className="glass-card">
              <div className="card-content">
                <div className="flex items-center justify-between mb-grid-4">
                  <h3 className="text-size-3 font-semibold">Line Items</h3>
                  <button
                    onClick={addLineItem}
                    className="btn-secondary hover-lift text-size-4"
                  >
                    + Add Item
                  </button>
                </div>

                <div className="space-y-grid-4">
                  {invoiceData.lineItems.map((item, index) => (
                    <div key={item.id} className="p-grid-4 bg-surface-2 rounded-lg">
                      <div className="flex items-center justify-between mb-grid-3">
                        <span className="text-size-4 text-muted-foreground">Item {index + 1}</span>
                        {invoiceData.lineItems.length > 1 && (
                          <button
                            onClick={() => removeLineItem(item.id)}
                            className="text-destructive hover:text-red-400 text-size-4"
                          >
                            Remove
                          </button>
                        )}
                      </div>

                      <div className="space-y-grid-3">
                        <div className="form-floating">
                          <input
                            type="text"
                            value={item.description}
                            onChange={(e) => updateLineItem(item.id, 'description', e.target.value)}
                            className="hover-glow"
                          />
                          <label>Description</label>
                        </div>

                        <div className="grid grid-cols-3 gap-grid-3">
                          <div className="form-floating">
                            <input
                              type="number"
                              min="0"
                              step="0.01"
                              value={item.quantity}
                              onChange={(e) => updateLineItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                              className="hover-glow"
                            />
                            <label>Qty</label>
                          </div>

                          <div className="form-floating">
                            <input
                              type="number"
                              min="0"
                              step="0.01"
                              value={item.unitPrice}
                              onChange={(e) => updateLineItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                              className="hover-glow"
                            />
                            <label>Price</label>
                          </div>

                          <div className="form-floating">
                            <input
                              type="number"
                              min="0"
                              max="100"
                              step="0.1"
                              value={item.discount}
                              onChange={(e) => updateLineItem(item.id, 'discount', parseFloat(e.target.value) || 0)}
                              className="hover-glow"
                            />
                            <label>Discount %</label>
                          </div>
                        </div>

                        <div className="text-right">
                          <span className="text-size-4 text-muted-foreground">Total: </span>
                          <span className="text-size-3 font-semibold">${item.total.toFixed(2)}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Live Preview */}
          <div className="glass-card">
            <div className="card-content">
              <h3 className="text-size-3 font-semibold mb-grid-4">Live Preview</h3>
              
              {/* Professional Invoice Preview */}
              <div className="bg-white text-black p-grid-6 rounded-lg text-size-4 space-y-grid-4">
                {/* Header */}
                <div className="flex justify-between items-start">
                  <div>
                    <h1 className="text-xl font-bold text-black">
                      {partnerData?.fromCompany?.name || "Your Company"}
                    </h1>
                    <div className="text-gray-600 mt-2 space-y-1">
                      <p>{partnerData?.fromCompany?.address || "Company Address"}</p>
                      <p>{partnerData?.fromCompany?.email || "<EMAIL>"}</p>
                      <p>{partnerData?.fromCompany?.phone || "+1-XXX-XXX-XXXX"}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <h2 className="text-lg font-bold text-black">
                      INVOICE #{deal.invoiceNumber || "DRAFT"}
                    </h2>
                    <p className="text-gray-600">
                      Date: {new Date().toLocaleDateString()}
                    </p>
                    <p className="text-gray-600">
                      Due: {new Date(invoiceData.dueDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                {/* Bill To */}
                <div className="border-t pt-grid-4">
                  <h3 className="font-semibold text-black mb-2">Bill To:</h3>
                  <div className="text-gray-700">
                    <p className="font-medium">{partnerData?.toCompany?.name || "IBC VENTURES LTD"}</p>
                    <p>{partnerData?.toCompany?.address || "Company Address"}</p>
                  </div>
                </div>

                {/* Line Items Table */}
                <div className="border-t pt-grid-4">
                  <table className="w-full text-xs border-collapse">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-2 py-1 text-left">Description</th>
                        <th className="border border-gray-300 px-2 py-1 text-center">Qty</th>
                        <th className="border border-gray-300 px-2 py-1 text-right">Price</th>
                        <th className="border border-gray-300 px-2 py-1 text-right">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {invoiceData.lineItems.map((item) => (
                        <tr key={item.id}>
                          <td className="border border-gray-300 px-2 py-1">
                            {item.description || "Service Description"}
                          </td>
                          <td className="border border-gray-300 px-2 py-1 text-center">
                            {item.quantity}
                          </td>
                          <td className="border border-gray-300 px-2 py-1 text-right">
                            ${item.unitPrice.toFixed(2)}
                          </td>
                          <td className="border border-gray-300 px-2 py-1 text-right font-medium">
                            ${item.total.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Totals */}
                <div className="border-t pt-grid-4">
                  <div className="flex justify-end">
                    <div className="w-64 space-y-2">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>${subtotal.toFixed(2)}</span>
                      </div>
                      {invoiceData.vatRate > 0 && (
                        <div className="flex justify-between">
                          <span>VAT ({invoiceData.vatRate}%):</span>
                          <span>${vatAmount.toFixed(2)}</span>
                        </div>
                      )}
                      <div className="flex justify-between font-bold text-lg border-t pt-2">
                        <span>Total:</span>
                        <span>${total.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Terms */}
                <div className="border-t pt-grid-4 text-gray-600">
                  <p><strong>Payment Terms:</strong> {invoiceData.paymentTerms}</p>
                  {invoiceData.notes && (
                    <div className="mt-2">
                      <p><strong>Notes:</strong></p>
                      <p className="whitespace-pre-wrap">{invoiceData.notes}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-grid-4 pt-grid-6 border-t">
          <button
            onClick={handleSave}
            disabled={isGenerating}
            className="btn-gradient hover-lift flex-1"
          >
            {isGenerating ? "⏳ Generating Invoice..." : "✨ Generate Invoice"}
          </button>
          <button
            onClick={onCancel}
            className="btn-secondary hover-lift"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}