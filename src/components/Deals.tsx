import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState } from "react";
import { toast } from "sonner";

interface DealsProps {
  setActiveTab?: (tab: string) => void;
}

export function Deals({ setActiveTab }: DealsProps) {
  const user = useQuery(api.users.myProfile);
  const deals = useQuery(api.deals.listForPartner, {});
  const createDeal = useMutation(api.deals.create);
  const updateDeal = useMutation(api.deals.update);
  const fixExistingDeals = useMutation(api.deals.fixExistingClosedDeals);
  
  const [isCreating, setIsCreating] = useState(false);
  const [formData, setFormData] = useState({
    partnerId: "",
    projectName: "",
    dealType: "",
    status: "in_progress" as "in_progress" | "closed" | "lost" | "paid",
    dealValueUsd: "",
    commissionPct: "",
    totalTokens: "",
    receivedTokens: "",
    liquidatedTokens: "",
    liquidationUsd: "",
    commissionDueTokenUsd: "",
    commissionDueFiatUsd: "",
    commissionPendingUsd: "",
    launched: false,
    closedBy: "",
    source: "",
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    paidFiatUsd: "",
    remainingFiatUsd: "",
    averageSellingPrice: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.partnerId || !formData.projectName || !formData.dealType) {
      toast.error("Partner, project name, and deal type are required");
      return;
    }

    try {
      await createDeal({
        partnerId: formData.partnerId as Id<"users">,
        projectName: formData.projectName,
        dealType: formData.dealType,
        status: formData.status,
        dealValueUsd: formData.dealValueUsd ? parseFloat(formData.dealValueUsd) : undefined,
        commissionPct: parseFloat(formData.commissionPct) || 5,
        totalTokens: formData.totalTokens ? parseFloat(formData.totalTokens) : undefined,
        receivedTokens: formData.receivedTokens ? parseFloat(formData.receivedTokens) : undefined,
        liquidatedTokens: formData.liquidatedTokens ? parseFloat(formData.liquidatedTokens) : undefined,
        liquidationUsd: formData.liquidationUsd ? parseFloat(formData.liquidationUsd) : undefined,
        commissionDueTokenUsd: formData.commissionDueTokenUsd ? parseFloat(formData.commissionDueTokenUsd) : undefined,
        commissionDueFiatUsd: formData.commissionDueFiatUsd ? parseFloat(formData.commissionDueFiatUsd) : undefined,
        commissionPendingUsd: formData.commissionPendingUsd ? parseFloat(formData.commissionPendingUsd) : undefined,
        launched: formData.launched,
        closedBy: formData.closedBy || undefined,
        source: formData.source || undefined,
        month: formData.month,
        year: formData.year,
        paidFiatUsd: formData.paidFiatUsd ? parseFloat(formData.paidFiatUsd) : undefined,
        remainingFiatUsd: formData.remainingFiatUsd ? parseFloat(formData.remainingFiatUsd) : undefined,
        averageSellingPrice: formData.averageSellingPrice ? parseFloat(formData.averageSellingPrice) : undefined,
      });
      toast.success("Deal created successfully");
      setFormData({
        partnerId: "",
        projectName: "",
        dealType: "",
        status: "in_progress",
        dealValueUsd: "",
        commissionPct: "",
        totalTokens: "",
        receivedTokens: "",
        liquidatedTokens: "",
        liquidationUsd: "",
        commissionDueTokenUsd: "",
        commissionDueFiatUsd: "",
        commissionPendingUsd: "",
        launched: false,
        closedBy: "",
        source: "",
        month: new Date().getMonth() + 1,
        year: new Date().getFullYear(),
        paidFiatUsd: "",
        remainingFiatUsd: "",
        averageSellingPrice: "",
      });
      setIsCreating(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to create deal");
    }
  };

  const handleStatusUpdate = async (dealId: Id<"deals">, status: "in_progress" | "closed" | "lost" | "paid") => {
    try {
      await updateDeal({ dealId, status });
      toast.success("Deal status updated");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update deal");
    }
  };

  const handleFieldUpdate = async (dealId: Id<"deals">, updates: any) => {
    try {
      await updateDeal({ dealId, ...updates });
      toast.success("Deal updated");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update deal");
    }
  };

  const handleFixCommissions = async () => {
    try {
      const result = await fixExistingDeals({});
      toast.success(`Fixed commissions for ${result.dealsFixed} deals`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to fix commissions");
    }
  };

  const handleViewInvoice = () => {
    setActiveTab?.("invoices");
  };

  if (!user || deals === undefined) {
    return (
      <div className="flex justify-center items-center py-grid-8">
        <div className="flex flex-col items-center gap-grid-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-accent border-t-transparent"></div>
          <p className="text-size-4" style={{ color: "var(--muted-foreground)" }}>Loading deals...</p>
        </div>
      </div>
    );
  }

  const canCreateDeals = user.role && ["sales", "ops", "accounting", "admin", "superadmin"].includes(user.role);
  const canUpdateDeals = user.role && ["sales", "ops", "accounting", "admin", "superadmin"].includes(user.role);
  const canFixCommissions = user.role && ["admin", "superadmin", "accounting"].includes(user.role);

  return (
    <div className="space-y-grid-8 animate-fade-in">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-grid-4">
        <div className="space-y-grid-2">
          <h1 className="text-size-1">Deals</h1>
          <div>
            <p className="text-size-3" style={{ color: "var(--muted-foreground)" }}>
              {user.role === "sales" 
                ? "Deals from your assigned partners" 
                : user.role === "partner"
                ? "Your deal pipeline and commissions"
                : "Track and manage your deal pipeline and commissions"
              }
            </p>
            {user.role === "sales" && (
              <div className="flex items-center gap-grid-2 mt-grid-2">
                <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                <p className="text-size-4 text-blue-400">Viewing deals from assigned partners only</p>
              </div>
            )}
          </div>
        </div>
        <div className="flex gap-grid-3">
          {canFixCommissions && (
            <button
              onClick={handleFixCommissions}
              className="btn-secondary hover-lift text-orange-400 border-orange-400 hover:bg-orange-400 hover:text-card-foreground"
            >
              🔧 Fix Commissions
            </button>
          )}
          {canCreateDeals && (
            <button
              onClick={() => setIsCreating(!isCreating)}
              className={`btn-gradient hover-lift ${isCreating ? 'bg-destructive hover:bg-destructive' : ''}`}
            >
              {isCreating ? "✕ Cancel" : "💼 Create New Deal"}
            </button>
          )}
        </div>
      </div>

      {canCreateDeals && isCreating && (
        <div className="glass-card animate-scale-in">
          <div className="card-content">
            <div className="flex items-center gap-grid-3 mb-grid-6">
              <div className="w-10 h-10 bg-gradient-accent rounded-lg flex items-center justify-center">
                <span className="text-accent-foreground text-size-2">💼</span>
              </div>
              <h2 className="text-size-2">Create New Deal</h2>
            </div>
            <form onSubmit={handleSubmit} className="space-y-grid-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-6">
              <div className="form-floating">
                <input
                  type="text"
                  value={formData.partnerId}
                  onChange={(e) => setFormData({ ...formData, partnerId: e.target.value })}
                  placeholder=" "
                  required
                  className="hover-glow"
                />
                <label>Partner ID *</label>
              </div>
              <div className="form-floating">
                <input
                  type="text"
                  value={formData.projectName}
                  onChange={(e) => setFormData({ ...formData, projectName: e.target.value })}
                  placeholder=" "
                  required
                  className="hover-glow"
                />
                <label>Project Name *</label>
              </div>
              <div className="form-floating">
                <input
                  type="text"
                  value={formData.dealType}
                  onChange={(e) => setFormData({ ...formData, dealType: e.target.value })}
                  placeholder=" "
                  required
                  className="hover-glow"
                />
                <label>Deal Type *</label>
              </div>
              <div>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
                  className="auth-input-field text-size-3 py-grid-3"
                >
                  <option value="in_progress">🔄 In Progress</option>
                  <option value="closed">✅ Closed</option>
                  <option value="lost">❌ Lost</option>
                  <option value="paid">💰 Paid</option>
                </select>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  step="0.01"
                  value={formData.dealValueUsd}
                  onChange={(e) => setFormData({ ...formData, dealValueUsd: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Deal Value (USD)</label>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  step="0.1"
                  value={formData.commissionPct}
                  onChange={(e) => setFormData({ ...formData, commissionPct: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Commission %</label>
              </div>
              <div className="flex items-center gap-grid-3">
                <input
                  type="checkbox"
                  id="launched"
                  checked={formData.launched}
                  onChange={(e) => setFormData({ ...formData, launched: e.target.checked })}
                  className="rounded"
                />
                <label htmlFor="launched" className="text-size-3">Project Launched?</label>
              </div>
              <div className="form-floating">
                <input
                  type="text"
                  value={formData.closedBy}
                  onChange={(e) => setFormData({ ...formData, closedBy: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Closed By</label>
              </div>
              <div className="form-floating">
                <input
                  type="text"
                  value={formData.source}
                  onChange={(e) => setFormData({ ...formData, source: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Source</label>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  min="1"
                  max="12"
                  value={formData.month}
                  onChange={(e) => setFormData({ ...formData, month: parseInt(e.target.value) })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Month</label>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  min="2020"
                  max="2030"
                  value={formData.year}
                  onChange={(e) => setFormData({ ...formData, year: parseInt(e.target.value) })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Year</label>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  step="0.01"
                  value={formData.totalTokens}
                  onChange={(e) => setFormData({ ...formData, totalTokens: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Total Tokens</label>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  step="0.01"
                  value={formData.receivedTokens}
                  onChange={(e) => setFormData({ ...formData, receivedTokens: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Received Tokens</label>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  step="0.01"
                  value={formData.liquidatedTokens}
                  onChange={(e) => setFormData({ ...formData, liquidatedTokens: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Liquidated Tokens</label>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  step="0.01"
                  value={formData.liquidationUsd}
                  onChange={(e) => setFormData({ ...formData, liquidationUsd: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Liquidation Value (USD)</label>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  step="0.01"
                  value={formData.paidFiatUsd}
                  onChange={(e) => setFormData({ ...formData, paidFiatUsd: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Paid Fiat (USD)</label>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  step="0.01"
                  value={formData.remainingFiatUsd}
                  onChange={(e) => setFormData({ ...formData, remainingFiatUsd: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Remaining Fiat (USD)</label>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  step="0.01"
                  value={formData.averageSellingPrice}
                  onChange={(e) => setFormData({ ...formData, averageSellingPrice: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Average Selling Price (USD)</label>
              </div>
            </div>
            <div className="flex gap-grid-4 pt-grid-4">
              <button
                type="submit"
                className="btn-gradient hover-lift flex-1 sm:flex-none"
              >
                ✨ Create Deal
              </button>
              <button
                type="button"
                onClick={() => setIsCreating(false)}
                className="btn-secondary hover-lift"
              >
                Cancel
              </button>
            </div>
            </form>
          </div>
        </div>
      )}

      {deals.length === 0 ? (
        <div className="glass-card">
          <div className="card-content text-center py-grid-8">
            <div className="w-16 h-16 bg-muted/20 rounded-full flex items-center justify-center mx-auto mb-grid-6">
              <span className="text-size-1">💼</span>
            </div>
            <h3 className="text-size-2 mb-grid-2">No deals found</h3>
            <p className="text-size-3 max-w-md mx-auto" style={{ color: "var(--muted-foreground)" }}>
              {user.role === "sales" 
                ? "No deals from your assigned partners yet. Deals will appear here once partners you're assigned to close deals."
                : user.role === "partner"
                ? "You haven't closed any deals yet. Submit leads and work with the sales team to close deals."
                : canCreateDeals 
                ? "Create your first deal to start tracking commissions and progress." 
                : "Deals will appear here once they're created."}
            </p>
            {canCreateDeals && !isCreating && (
              <button
                onClick={() => setIsCreating(true)}
                className="btn-gradient hover-lift mt-grid-6"
              >
                💼 Create Your First Deal
              </button>
            )}
          </div>
        </div>
      ) : (
        <div className="glass-card overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full border-collapse text-size-4">
              <thead>
                <tr style={{ backgroundColor: "var(--surface-2)", borderBottom: "1px solid var(--border-default)" }}>
                  <th className="px-3 py-3 text-left font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Project</th>
                  <th className="px-3 py-3 text-left font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Launched?</th>
                  <th className="px-3 py-3 text-right font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Deal Value</th>
                  <th className="px-3 py-3 text-left font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Closed By</th>
                  <th className="px-3 py-3 text-left font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Source?</th>
                  <th className="px-3 py-3 text-left font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Deal Type</th>
                  <th className="px-3 py-3 text-center font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Month</th>
                  <th className="px-3 py-3 text-center font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Year</th>
                  <th className="px-3 py-3 text-right font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Total # Tokens</th>
                  <th className="px-3 py-3 text-right font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Received # Tokens</th>
                  <th className="px-3 py-3 text-right font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Liquidated # Tokens</th>
                  <th className="px-3 py-3 text-right font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Liquidation Value</th>
                  <th className="px-3 py-3 text-right font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Due - Fiat</th>
                  <th className="px-3 py-3 text-right font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Paid - Fiat</th>
                  <th className="px-3 py-3 text-right font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Remaining - Fiat</th>
                  <th className="px-3 py-3 text-right font-semibold" style={{ color: "var(--foreground)", borderRight: "1px solid var(--border-subtle)" }}>Average Selling Price</th>
                  <th className="px-3 py-3 text-center font-semibold" style={{ color: "var(--foreground)" }}>Invoice</th>
                </tr>
              </thead>
              <tbody>
                {deals.map((deal, index) => {
                  const dueFiat = (deal.commissionDueFiatUsd || 0) + (deal.commissionDueTokenUsd || 0);
                  const paidFiat = deal.paidFiatUsd || 0;
                  const remainingFiat = dueFiat - paidFiat;
                  const totalCommission = dueFiat; // Total commission for this deal
                  
                  return (
                    <>
                    <tr 
                      key={deal._id}
                      className="hover:bg-surface-2 transition-colors"
                      style={{ borderBottom: "1px solid var(--border-subtle)" }}
                    >
                      <td className="px-3 py-3" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        <div className="font-semibold text-foreground">{deal.projectName}</div>
                      </td>
                      <td className="px-3 py-3 text-center" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {canUpdateDeals ? (
                          <input
                            type="checkbox"
                            checked={deal.launched || false}
                            onChange={(e) => handleFieldUpdate(deal._id, { launched: e.target.checked })}
                            className="rounded"
                          />
                        ) : (
                          <span>{deal.launched ? "✓" : ""}</span>
                        )}
                      </td>
                      <td className="px-3 py-3 text-right" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {deal.dealValueUsd ? `$${deal.dealValueUsd.toLocaleString()}` : "-"}
                      </td>
                      <td className="px-3 py-3" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {deal.closedBy || "-"}
                      </td>
                      <td className="px-3 py-3" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {deal.source || "-"}
                      </td>
                      <td className="px-3 py-3" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        <span className={`px-2 py-1 rounded text-size-4 ${
                          deal.dealType.toLowerCase() === "marketing" ? "bg-blue-500/20 text-blue-400" :
                          deal.dealType.toLowerCase() === "investment" ? "bg-green-500/20 text-green-400" :
                          "bg-gray-500/20 text-gray-400"
                        }`}>
                          {deal.dealType}
                        </span>
                      </td>
                      <td className="px-3 py-3 text-center" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {deal.month || "-"}
                      </td>
                      <td className="px-3 py-3 text-center" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {deal.year || "-"}
                      </td>
                      <td className="px-3 py-3 text-right" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {deal.totalTokens ? deal.totalTokens.toLocaleString() : "-"}
                      </td>
                      <td className="px-3 py-3 text-right" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {deal.receivedTokens ? deal.receivedTokens.toLocaleString() : "-"}
                      </td>
                      <td className="px-3 py-3 text-right" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {deal.liquidatedTokens ? deal.liquidatedTokens.toLocaleString() : "-"}
                      </td>
                      <td className="px-3 py-3 text-right" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {deal.liquidationUsd ? `$${deal.liquidationUsd.toLocaleString()}` : "-"}
                      </td>
                      <td className="px-3 py-3 text-right" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {dueFiat > 0 ? `$${dueFiat.toLocaleString()}` : "-"}
                      </td>
                      <td className="px-3 py-3 text-right" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {paidFiat > 0 ? `$${paidFiat.toLocaleString()}` : "$0.00"}
                      </td>
                      <td className="px-3 py-3 text-right" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {remainingFiat > 0 ? `$${remainingFiat.toLocaleString()}` : "$0.00"}
                      </td>
                      <td className="px-3 py-3 text-right" style={{ borderRight: "1px solid var(--border-subtle)" }}>
                        {deal.averageSellingPrice ? `$${deal.averageSellingPrice.toFixed(2)}` : "-"}
                      </td>
                      <td className="px-3 py-3 text-center">
                        {deal.invoiceNumber ? (
                          <button
                            onClick={handleViewInvoice}
                            className="btn-secondary hover-lift text-size-4 px-grid-2 py-grid-1"
                            title="View Invoice in Invoices Section"
                          >
                            📄 View
                          </button>
                        ) : (
                          totalCommission > 0 && ["closed", "paid"].includes(deal.status) ? (
                            <button
                              onClick={handleViewInvoice}
                              className="btn-gradient hover-lift text-size-4 px-grid-2 py-grid-1"
                              title="Create Invoice in Invoices Section"
                            >
                              ✨ Create
                            </button>
                          ) : (
                            <span className="text-muted-foreground text-size-4">-</span>
                          )
                        )}
                      </td>
                    </tr>
                  </>
                );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
