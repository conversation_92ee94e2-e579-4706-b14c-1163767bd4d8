import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState } from "react";
import { toast } from "sonner";
import { InvoiceGenerator } from "./InvoiceGenerator";

export function Invoices() {
  const user = useQuery(api.users.myProfile);
  const invoices = useQuery(api.deals.listInvoices, {});
  const updateInvoiceStatus = useMutation(api.deals.updateInvoiceStatus);
  
  const [selectedInvoice, setSelectedInvoice] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Handle loading states
  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (!invoices) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="flex flex-col items-center gap-grid-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
          <p className="text-size-4 text-muted">Loading invoices...</p>
        </div>
      </div>
    );
  }

  // Filter invoices based on search and status
  const filteredInvoices = invoices.filter((invoice) => {
    const matchesSearch = !searchQuery || 
      invoice.invoiceNumber?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invoice.projectName.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || 
      (invoice.invoiceStatus || "draft") === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const formatDate = (timestamp?: number) => {
    if (!timestamp) return "N/A";
    return new Date(timestamp).toLocaleDateString();
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "draft": return "bg-gray-500/20 text-gray-400";
      case "sent": return "bg-blue-500/20 text-blue-400";
      case "paid": return "bg-green-500/20 text-green-400";
      case "overdue": return "bg-red-500/20 text-red-400";
      case "cancelled": return "bg-gray-500/20 text-gray-400";
      default: return "bg-gray-500/20 text-gray-400";
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case "draft": return "📝";
      case "sent": return "📤";
      case "paid": return "✅";
      case "overdue": return "⚠️";
      case "cancelled": return "❌";
      default: return "📄";
    }
  };

  const handleStatusUpdate = async (invoiceId: Id<"deals">, newStatus: "draft" | "sent" | "paid" | "overdue" | "cancelled") => {
    try {
      await updateInvoiceStatus({
        dealId: invoiceId,
        status: newStatus,
      });
      toast.success("Invoice status updated");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update status");
    }
  };

  const totalInvoices = filteredInvoices.length;
  const totalAmount = filteredInvoices.reduce((sum, inv) => 
    sum + (inv.commissionDueFiatUsd || 0) + (inv.commissionDueTokenUsd || 0), 0
  );
  const paidAmount = filteredInvoices
    .filter(inv => inv.invoiceStatus === "paid")
    .reduce((sum, inv) => sum + (inv.commissionDueFiatUsd || 0) + (inv.commissionDueTokenUsd || 0), 0);

  return (
    <div className="space-y-grid-6">
      {/* Header with Stats */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-grid-4">
        <div>
          <h1 className="text-size-1 mb-grid-2">Invoice Management</h1>
          <p className="text-size-3 text-muted-foreground">
            Manage and track all your invoices
          </p>
        </div>
        
        <div className="grid grid-cols-3 gap-grid-4">
          <div className="glass-card">
            <div className="card-content text-center">
              <div className="text-size-2 font-semibold">{totalInvoices}</div>
              <div className="text-size-4 text-muted-foreground">Total Invoices</div>
            </div>
          </div>
          <div className="glass-card">
            <div className="card-content text-center">
              <div className="text-size-2 font-semibold">${totalAmount.toLocaleString()}</div>
              <div className="text-size-4 text-muted-foreground">Total Value</div>
            </div>
          </div>
          <div className="glass-card">
            <div className="card-content text-center">
              <div className="text-size-2 font-semibold text-green-400">${paidAmount.toLocaleString()}</div>
              <div className="text-size-4 text-muted-foreground">Paid Amount</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="glass-card">
        <div className="card-content">
          <div className="flex flex-col sm:flex-row gap-grid-4">
            <div className="flex-1">
              <div className="form-floating">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Search invoices by number or project name</label>
              </div>
            </div>
            
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="auth-input-field text-size-3 py-grid-3 w-full"
              >
                <option value="all">All Status</option>
                <option value="draft">📝 Draft</option>
                <option value="sent">📤 Sent</option>
                <option value="paid">✅ Paid</option>
                <option value="overdue">⚠️ Overdue</option>
                <option value="cancelled">❌ Cancelled</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Invoice List */}
      {filteredInvoices.length === 0 ? (
        <div className="glass-card">
          <div className="card-content text-center py-grid-12">
            <div className="w-16 h-16 bg-muted/10 rounded-full flex items-center justify-center mx-auto mb-grid-4">
              <span className="text-size-1">📄</span>
            </div>
            <h3 className="text-size-2 font-semibold mb-grid-2">No invoices found</h3>
            <p className="text-muted-foreground mb-grid-6">
              {searchQuery || statusFilter !== "all" 
                ? "Try adjusting your filters to see more invoices."
                : "You haven't generated any invoices yet. Create deals and generate invoices from the Deals section."}
            </p>
          </div>
        </div>
      ) : (
        <div className="space-y-grid-4">
          {filteredInvoices.map((invoice) => {
            const totalCommission = (invoice.commissionDueFiatUsd || 0) + (invoice.commissionDueTokenUsd || 0);
            const isExpanded = selectedInvoice === invoice._id;
            
            return (
              <div key={invoice._id} className="glass-card">
                <div className="card-content">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-grid-4">
                      <div className="w-12 h-12 bg-gradient-accent rounded-lg flex items-center justify-center">
                        <span className="text-accent-foreground text-size-3">
                          {getStatusIcon(invoice.invoiceStatus)}
                        </span>
                      </div>
                      
                      <div>
                        <div className="flex items-center gap-grid-3 mb-grid-1">
                          <h3 className="text-size-2 font-semibold">
                            {invoice.invoiceNumber ? `Invoice #${invoice.invoiceNumber}` : `Draft Invoice`}
                          </h3>
                          <span className={`px-2 py-1 rounded text-size-4 ${getStatusColor(invoice.invoiceStatus)}`}>
                            {(invoice.invoiceStatus || "draft").charAt(0).toUpperCase() + (invoice.invoiceStatus || "draft").slice(1)}
                          </span>
                        </div>
                        <p className="text-size-3 text-muted-foreground">
                          {invoice.projectName} • ${totalCommission.toLocaleString()}
                        </p>
                        <div className="flex items-center gap-grid-4 text-size-4 text-muted-foreground mt-grid-1">
                          <span>Generated: {formatDate(invoice.invoiceGeneratedAt)}</span>
                          <span>Due: {formatDate(invoice.invoiceDueDate)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-grid-2">
                      {invoice.invoiceStatus === "draft" && (
                        <button
                          onClick={() => handleStatusUpdate(invoice._id, "sent")}
                          className="btn-secondary hover-lift"
                        >
                          📤 Send
                        </button>
                      )}
                      
                      {invoice.invoiceStatus === "sent" && (
                        <button
                          onClick={() => handleStatusUpdate(invoice._id, "paid")}
                          className="btn-gradient hover-lift"
                        >
                          ✅ Mark Paid
                        </button>
                      )}
                      
                      <button
                        onClick={() => setSelectedInvoice(isExpanded ? null : invoice._id)}
                        className="btn-secondary hover-lift"
                      >
                        {isExpanded ? "Close" : "Manage"}
                      </button>
                    </div>
                  </div>
                  
                  {/* Expanded Invoice Management */}
                  {isExpanded && (
                    <div className="mt-grid-6 pt-grid-6 border-t border-border">
                      <InvoiceGenerator 
                        deal={invoice}
                        onInvoiceGenerated={() => {
                          // Refresh will happen automatically due to Convex reactivity
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}