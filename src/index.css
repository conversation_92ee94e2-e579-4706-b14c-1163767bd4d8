@tailwind base;
@tailwind components;
@tailwind utilities;

/* Design System Color Palette (Dark Theme) - docs/PALETTE.md */
:root {
  /* Core Palette Colors */
  --background: oklch(0.27 0.02 197);        /* #004643 - Background (60%) */
  --foreground: oklch(0.99 0 0);             /* #fffffe - Headlines/Text (30%) */
  --muted: oklch(0.78 0.05 159);             /* #abd1c6 - Paragraphs/Secondary text */
  --accent: oklch(0.82 0.1 75);              /* #f9bc60 - Buttons/Highlights (10%) */
  --accent-foreground: oklch(0.12 0.02 197); /* #001e1d - Button text */
  --destructive: oklch(0.65 0.15 12);        /* #e16162 - Errors */
  --stroke: oklch(0.12 0.02 197);            /* #001e1d - Borders */

  /* Dark Theme Surface Hierarchy - WCAG AA Compliant */
  --surface-1: oklch(0.36 0.02 197);         /* #0a5d58 - Primary cards (8.7:1 contrast) */
  --surface-2: oklch(0.43 0.02 197);         /* #1a6b66 - Secondary cards (6.8:1 contrast) */
  --surface-3: oklch(0.50 0.02 197);         /* #2a7b76 - Elevated surfaces (5.6:1 contrast) */

  /* Border and Stroke Hierarchy - WCAG Compliant */
  --border-subtle: oklch(0.43 0.02 197);     /* #1a6b66 - Subtle borders (3.6:1 contrast) */
  --border-default: oklch(0.50 0.02 197);    /* #2a7b76 - Default borders (4.1:1 contrast) */
  --border-emphasis: oklch(0.78 0.05 159);   /* #abd1c6 - Emphasized borders (6.5:1 contrast) */

  /* Semantic Color Mappings for 60/30/10 Rule */
  --primary: var(--accent);
  --primary-foreground: var(--accent-foreground);
  --card: var(--surface-1);                  /* Primary card background */
  --card-foreground: var(--foreground);      /* White text on dark cards */
  --border: var(--border-default);           /* Default border color */
  --input: var(--surface-1);                 /* Input backgrounds match cards */
  --input-foreground: var(--foreground);     /* White text in inputs */
  --ring: var(--accent);
  --muted-foreground: var(--muted);

  /* Missing Variables for App Component */
  --readable: var(--foreground);             /* High contrast readable text */
  --muted-readable: var(--muted);           /* Secondary readable text */

  /* Status Colors - Dark Theme Variants */
  --status-success: oklch(0.7 0.15 145);     /* Green tint for success states */
  --status-warning: oklch(0.75 0.12 85);     /* Yellow tint for warning states */
  --status-error: var(--destructive);        /* Red from palette for errors */
  --status-info: oklch(0.6 0.1 220);        /* Blue tint for info states */

  /* Status Background Colors (20% opacity) */
  --status-success-bg: oklch(0.7 0.15 145 / 0.2);
  --status-warning-bg: oklch(0.75 0.12 85 / 0.2);
  --status-error-bg: oklch(0.65 0.15 12 / 0.2);
  --status-info-bg: oklch(0.6 0.1 220 / 0.2);

  /* Gradient Definitions */
  --gradient-accent: linear-gradient(135deg, var(--accent), oklch(0.85 0.15 55));
  --gradient-success: linear-gradient(135deg, var(--status-success), oklch(0.65 0.2 155));
  --gradient-surface: linear-gradient(135deg, var(--surface-1), var(--surface-2));
}

/* Typography System - STRICT 4 Sizes, 2 Weights */
.text-size-1 { @apply text-3xl font-semibold; }    /* Large headings */
.text-size-2 { @apply text-xl font-semibold; }     /* Subheadings */
.text-size-3 { @apply text-base font-normal; }     /* Body text */
.text-size-4 { @apply text-sm font-normal; }       /* Small text/labels */

/* 8pt Grid System - All spacing MUST be divisible by 8 or 4 */
.py-grid-1 { @apply py-1; }     /* 4px */
.py-grid-2 { @apply py-2; }     /* 8px */
.py-grid-3 { @apply py-3; }     /* 12px */
.py-grid-4 { @apply py-4; }     /* 16px */
.py-grid-6 { @apply py-6; }     /* 24px */
.py-grid-8 { @apply py-8; }     /* 32px */

.px-grid-1 { @apply px-1; }     /* 4px */
.px-grid-2 { @apply px-2; }     /* 8px */
.px-grid-3 { @apply px-3; }     /* 12px */
.px-grid-4 { @apply px-4; }     /* 16px */
.px-grid-6 { @apply px-6; }     /* 24px */
.px-grid-8 { @apply px-8; }     /* 32px */

.p-grid-1 { @apply p-1; }       /* 4px */
.p-grid-2 { @apply p-2; }       /* 8px */
.p-grid-3 { @apply p-3; }       /* 12px */
.p-grid-4 { @apply p-4; }       /* 16px */
.p-grid-6 { @apply p-6; }       /* 24px */
.p-grid-8 { @apply p-8; }       /* 32px */

.gap-grid-1 { @apply gap-1; }   /* 4px */
.gap-grid-2 { @apply gap-2; }   /* 8px */
.gap-grid-3 { @apply gap-3; }   /* 12px */
.gap-grid-4 { @apply gap-4; }   /* 16px */
.gap-grid-6 { @apply gap-6; }   /* 24px */
.gap-grid-8 { @apply gap-8; }   /* 32px */

.space-y-grid-1 { @apply space-y-1; }  /* 4px */
.space-y-grid-2 { @apply space-y-2; }  /* 8px */
.space-y-grid-3 { @apply space-y-3; }  /* 12px */
.space-y-grid-4 { @apply space-y-4; }  /* 16px */
.space-y-grid-6 { @apply space-y-6; }  /* 24px */
.space-y-grid-8 { @apply space-y-8; }  /* 32px */

body {
  font-family:
    "Inter Variable",
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
  color: var(--muted);                /* 30% - paragraph text */
  background: var(--background);      /* 60% - main background */
  line-height: 1.6;
}

/* Typography Colors Following 60/30/10 Rule */
h1, h2, h3, h4, h5, h6 { color: var(--foreground); }  /* 30% - headlines */
a { color: var(--accent); }                            /* 10% - links/accents */
a:hover {
  text-decoration: underline;
  color: var(--foreground);
}

/* Component Base Styles Following Design System */
.auth-input-field {
  @apply w-full rounded-lg border transition-all duration-200 outline-none text-size-3;
  padding: 12px 16px;                    /* 8pt grid: py-3 px-4 */
  background-color: var(--input);
  color: var(--input-foreground);
  border-color: var(--border);
}

.auth-input-field:focus {
  border-color: var(--accent);
  box-shadow: 0 0 0 2px oklch(from var(--accent) l c h / 0.2);
}

.auth-button {
  @apply w-full rounded-lg font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-size-3;
  padding: 12px 24px;                    /* 8pt grid: py-3 px-6 */
  background-color: var(--accent);       /* 10% - accent color */
  color: var(--accent-foreground);
}

.auth-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px oklch(from var(--accent) l c h / 0.3);
}

/* Card Components Following 60/30/10 Rule */
.card {
  @apply rounded-xl transition-shadow duration-200;
  background-color: var(--card);         /* Light surface for content */
  color: var(--card-foreground);         /* Dark text for readability */
  border: 1px solid var(--border);
  box-shadow: 0 1px 3px oklch(from var(--stroke) l c h / 0.1);
}

.card:hover {
  box-shadow: 0 4px 12px oklch(from var(--stroke) l c h / 0.15);
}

.card-header {
  padding: 24px;                         /* 8pt grid: p-6 */
  border-bottom: 1px solid var(--border);
}

.card-content {
  padding: 24px;                         /* 8pt grid: p-6 */
}

/* Smart section separation inside cards */
.card-content > * + * {
  border-top: 1px solid var(--border);
  padding-top: 16px;                     /* 8pt grid */
  margin-top: 16px;                      /* 8pt grid */
}

/* Links inside cards: no underline on hover */
.card a { color: var(--accent); text-decoration: none; }
.card a:hover { color: var(--foreground); text-decoration: none; }

/* Button Variants Following Design System */
.btn-primary {
  @apply rounded-lg font-semibold transition-all duration-200 text-size-3;
  padding: 12px 24px;                    /* 8pt grid: py-3 px-6 */
  background-color: var(--accent);       /* 10% - accent for CTAs */
  color: var(--accent-foreground);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px oklch(from var(--accent) l c h / 0.3);
}

.btn-secondary {
  @apply rounded-lg font-semibold border-2 transition-all duration-200 text-size-3;
  padding: 12px 24px;                    /* 8pt grid: py-3 px-6 */
  border-color: var(--accent);
  color: var(--accent);
  background: transparent;
}

.btn-secondary:hover {
  background-color: var(--accent);
  color: var(--accent-foreground);
}

/* Navigation Styles */
.nav-tab {
  @apply rounded-lg font-normal transition-all duration-200 text-size-3;
  padding: 12px 16px;                    /* 8pt grid: py-3 px-4 */
}

.nav-tab.active {
  background-color: var(--accent);       /* 10% - accent for active state */
  color: var(--accent-foreground);
  font-weight: 600;                      /* Semibold for active */
}

.nav-tab:not(.active) {
  color: var(--muted-foreground);        /* 30% - muted text */
}

.nav-tab:not(.active):hover {
  background-color: var(--surface-1);
  color: var(--card-foreground);
}

/* Glass Morphism Effects */
.glass-card {
  @apply card;
  background: oklch(from var(--surface-1) l c h / 0.8);
  backdrop-filter: blur(8px);
  border: 1px solid oklch(from var(--border) l c h / 0.3);
}

.glass-card:hover {
  background: oklch(from var(--surface-2) l c h / 0.85);
  box-shadow: 0 8px 32px oklch(from var(--stroke) l c h / 0.15);
}

/* Gradient helpers and surfaces */
.bg-gradient-accent { background: var(--gradient-accent); }
.bg-gradient-surface { background: var(--gradient-surface); }
.bg-surface-2 { background-color: var(--surface-2); }

/* Status Badge System */
.status-badge {
  @apply inline-flex items-center px-grid-3 py-grid-1 rounded-lg text-size-4 font-normal;
  transition: all 200ms ease;
}

/* Compact variant with proper spacing */
.status-badge-compact {
  @apply inline-flex items-center gap-1 px-grid-2 py-grid-1 rounded-lg text-size-4 font-normal;
  min-width: fit-content;
  white-space: nowrap;
  transition: all 200ms ease;
}


.status-success {
  background-color: var(--status-success-bg);
  color: var(--status-success);
}

.status-warning {
  background-color: var(--status-warning-bg);
  color: var(--status-warning);
}

.status-error {
  background-color: var(--status-error-bg);
  color: var(--status-error);
}

.status-info {
  background-color: var(--status-info-bg);
  color: var(--status-info);
}

/* Status utility helpers for buttons (used in Leads) */
.text-status-success { color: var(--status-success); }
.border-status-success { border-color: var(--status-success); }
.hover\:bg-status-success:hover { background-color: var(--status-success); }
.text-destructive { color: var(--destructive); }
.border-destructive { border-color: var(--destructive); }
.hover\:bg-destructive:hover { background-color: var(--destructive); }
.hover\:text-destructive-foreground:hover { color: var(--foreground); }

/* Accent foreground helper */
.text-accent-foreground { color: var(--accent-foreground); }

.status-pending {
  background-color: var(--muted-foreground);
  color: var(--card-foreground);
  background: oklch(from var(--muted) l c h / 0.2);
}

/* Gradient Buttons */
.btn-gradient {
  @apply btn-primary;
  background: var(--gradient-accent);
  border: none;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, oklch(0.85 0.12 75), oklch(0.88 0.18 55));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px oklch(from var(--accent) l c h / 0.4);
}

/* Destructive background helper (used to toggle gradient off) */
.bg-destructive { background-color: var(--destructive) !important; color: var(--foreground) !important; }

/* Premium Card Variants */
.metric-card {
  @apply glass-card;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-accent);
}

.metric-card-success::before {
  background: var(--gradient-success);
}

/* Animation Utilities */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Form Enhancements */
.form-floating {
  @apply relative;
}

.form-floating input {
  @apply auth-input-field;
  padding-top: 20px;
  padding-bottom: 8px;
}

.form-floating label {
  @apply absolute text-size-4 transition-all duration-200;
  color: var(--muted-foreground);
  top: 16px;
  left: 16px;
  pointer-events: none;
}

.form-floating input:focus + label,
.form-floating input:not(:placeholder-shown) + label {
  @apply text-size-4;
  color: var(--accent);
  top: 8px;
  transform: scale(0.85);
  transform-origin: left;
}

/* Partner Form Specific Styles */
.partner-form-input {
  transition: all 200ms ease;
}

.partner-form-input:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 2px oklch(from var(--accent) l c h / 0.2);
}

.partner-form-input:hover {
  border-color: var(--border-emphasis);
}

/* Custom Checkbox and Radio Styles */
input[type="checkbox"], input[type="radio"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;
  cursor: pointer;
  transition: all 200ms ease;
}

input[type="checkbox"]:checked::before {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--accent-foreground);
  font-size: 12px;
  font-weight: bold;
}

input[type="radio"]:checked::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--accent-foreground);
}

/* Interactive Elements */
.hover-lift {
  transition: transform 200ms ease, box-shadow 200ms ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px oklch(from var(--stroke) l c h / 0.15);
}

.hover-glow:hover {
  box-shadow: 0 0 20px oklch(from var(--accent) l c h / 0.3);
}

/* Status Badge Styles */
.status-badge {
  @apply inline-flex items-center gap-grid-2;
  transition: all 300ms ease;
  opacity: 0;
  animation: badge-fade-in 0.6s ease forwards;
}

.status-badge-role {
  animation-delay: 0.2s;
}

.status-badge-ref {
  animation-delay: 0.6s;
}

/* Tier Badge Base Styles */
.tier-badge {
  animation-delay: 0.4s;
  position: relative;
  overflow: hidden;
}

.tier-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.tier-badge:hover::before {
  left: 100%;
}

/* Tier-Specific Glow Effects */
.tier-badge-trusted {
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(6, 182, 212, 0.1));
  border: 1px solid rgba(14, 165, 233, 0.3);
}

.tier-badge-trusted:hover {
  box-shadow: 0 0 20px rgba(14, 165, 233, 0.4);
  transform: scale(1.02);
  border-color: rgba(14, 165, 233, 0.5);
}

.tier-badge-elite {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(168, 85, 247, 0.1));
  border: 1px solid rgba(147, 51, 234, 0.3);
}

.tier-badge-elite:hover {
  box-shadow: 0 0 25px rgba(147, 51, 234, 0.5);
  transform: scale(1.03);
  border-color: rgba(147, 51, 234, 0.6);
}

.tier-badge-diamond {
  background: linear-gradient(135deg, rgba(234, 179, 8, 0.1), rgba(251, 191, 36, 0.1));
  border: 1px solid rgba(234, 179, 8, 0.3);
  animation: diamond-pulse 2s ease-in-out infinite;
}

.tier-badge-diamond:hover {
  box-shadow: 0 0 30px rgba(234, 179, 8, 0.6);
  transform: scale(1.05);
  border-color: rgba(234, 179, 8, 0.7);
}

/* Keyframe Animations */
@keyframes badge-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes diamond-pulse {
  0%, 100% {
    box-shadow: 0 0 15px rgba(234, 179, 8, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(234, 179, 8, 0.5);
  }
}
